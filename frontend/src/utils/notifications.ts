/**
 * Browser notification utilities for job completion notifications
 */

export type NotificationPermission = 'default' | 'granted' | 'denied';

/**
 * Check if browser notifications are supported
 */
export const isNotificationSupported = (): boolean => {
  return 'Notification' in window;
};

/**
 * Get current notification permission status
 */
export const getNotificationPermission = (): NotificationPermission => {
  if (!isNotificationSupported()) {
    return 'denied';
  }
  return Notification.permission as NotificationPermission;
};

/**
 * Request notification permission from the user
 */
export const requestNotificationPermission = async (): Promise<NotificationPermission> => {
  if (!isNotificationSupported()) {
    return 'denied';
  }

  try {
    const permission = await Notification.requestPermission();
    return permission as NotificationPermission;
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return 'denied';
  }
};

/**
 * Show a browser notification
 */
export const showNotification = (title: string, options?: NotificationOptions): Notification | null => {
  console.log('showNotification called with:', { title, options });
  console.log('Notification supported:', isNotificationSupported());
  console.log('Notification permission:', getNotificationPermission());

  if (!isNotificationSupported()) {
    console.warn('Notifications not supported in this browser');
    return null;
  }

  if (getNotificationPermission() !== 'granted') {
    console.warn('Notification permission not granted. Current permission:', getNotificationPermission());
    return null;
  }

  try {
    // Use the Gallery Tuner icon for notifications, respecting user's theme preference
    const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const iconUrl = `${window.location.origin}/gallery_tuner_${isDarkMode ? 'dark' : 'light'}_mode.png`;

    console.log('Creating notification with icon:', iconUrl);

    const notification = new Notification(title, {
      icon: iconUrl,
      badge: iconUrl,
      ...options
    });

    console.log('Notification created successfully:', notification);

    // Auto-close notification after 5 seconds
    setTimeout(() => {
      notification.close();
    }, 5000);

    return notification;
  } catch (error) {
    console.error('Error showing notification:', error);
    return null;
  }
};

/**
 * Show a job completion notification
 */
export const showJobCompletionNotification = (
  filename: string,
  success: boolean,
  onClick?: () => void
): Notification | null => {
  const title = success 
    ? 'Compression Complete!' 
    : 'Compression Failed';
  
  const body = success
    ? `${filename} has been compressed successfully.`
    : `Failed to compress ${filename}.`;

  const notification = showNotification(title, {
    body,
    tag: `job-${filename}`, // Prevent duplicate notifications for same file
    requireInteraction: false,
    silent: false
  });

  if (notification && onClick) {
    notification.onclick = () => {
      onClick();
      notification.close();
      // Focus the window when notification is clicked
      window.focus();
    };
  }

  return notification;
};

/**
 * Check if user has previously been prompted for notifications
 */
export const hasBeenPromptedForNotifications = (): boolean => {
  try {
    return localStorage.getItem('notification-prompted') === 'true';
  } catch (error) {
    console.warn('Failed to read notification-prompted from localStorage:', error);
    return false;
  }
};

/**
 * Mark that user has been prompted for notifications
 */
export const markAsPromptedForNotifications = (): void => {
  try {
    localStorage.setItem('notification-prompted', 'true');
  } catch (error) {
    console.warn('Failed to save notification-prompted to localStorage:', error);
  }
};

/**
 * Check if notifications should be shown based on user preference and browser permission
 */
export const shouldShowNotifications = (userPreference: boolean): boolean => {
  return userPreference && 
         isNotificationSupported() && 
         getNotificationPermission() === 'granted';
};
